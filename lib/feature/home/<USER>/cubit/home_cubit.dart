
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/global_city_cubit/global_city_cubit.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'dart:developer' as developer;

part 'home_state.dart';







class HomeCubit extends Cubit<HomeState> {
  final DioConsumer _dioConsumer;
  final LocationService _locationService;
  // Flags to prevent duplicate API calls
  bool _isInitializing = false;
  bool _isFetchingCategories = false;
  bool _isFetchingReels = false;
  bool _isDisposed = false;

  // Track last initialization time to prevent rapid re-initialization
  DateTime? _lastInitTime;

  HomeCubit({
    required DioConsumer dioConsumer,
    required LocationService locationService,
  })  : _dioConsumer = dioConsumer,
        _locationService = locationService,
        super(HomeInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(HomeState state) {
    if (!isClosed && !_isDisposed) {
      emit(state);
    }
  }

  @override
  Future<void> close() {
    _isDisposed = true;
    return super.close();
  }

  /// Initialize home data with optimized loading
  Future<void> initializeHome() async {
    // Prevent multiple initialization calls
    if (_isInitializing) {
      developer.log('Home initialization already in progress, skipping', name: 'HomeCubit');
      return;
    }

    // Prevent rapid re-initialization (debounce mechanism)
    final now = DateTime.now();
    if (_lastInitTime != null && now.difference(_lastInitTime!).inSeconds < 2) {
      developer.log('Home initialization called too soon, skipping', name: 'HomeCubit');
      return;
    }
    _lastInitTime = now;

    _isInitializing = true;
    _safeEmit(HomeLoading());

    try {
      // First, load cities (this must happen first)
      await loadCitiesAndSelectClosest();

      // Get current state to access selected city
      final currentState = state;
      if (currentState is HomeLoaded) {
        // Initialize with empty data and loading states to prevent "no result" flash
        _safeEmit(currentState.copyWith(
          categories: [], // Keep empty to show shimmer
          reels: [], // Keep empty to show shimmer
          isLoadingCategories: true,
          isLoadingReels: true,
        ));

        // Load categories first (faster), then reels with better error handling
        try {
          await fetchCategories(currentState.currentCity?.id, setLoading: false);
        } catch (e) {
          developer.log('Error loading categories: $e', name: 'HomeCubit');
          // Continue with reels even if categories fail
          final state = this.state;
          if (state is HomeLoaded) {
            _safeEmit(state.copyWith(isLoadingCategories: false));
          }
        }

        // Small delay to prevent overwhelming low-memory devices
        await Future.delayed(const Duration(milliseconds: 200));

        try {
          await fetchReels(currentState.currentCity?.id, setLoading: false);
        } catch (e) {
          developer.log('Error loading reels: $e', name: 'HomeCubit');
          // Set reels loading to false even if it fails
          final state = this.state;
          if (state is HomeLoaded) {
            _safeEmit(state.copyWith(isLoadingReels: false));
          }
        }
      }
    } catch (e) {
      _safeEmit(HomeError('Failed to initialize home: ${e.toString()}'));
    } finally {
      _isInitializing = false;
    }
  }

  /// Load cities and select closest based on location
  Future<void> loadCitiesAndSelectClosest() async {
    try {
      final hasPermission = await _locationService.checkLocationPermissions();

      if (hasPermission) {
        // User granted location permission - get current location
        try {
          final position = await Geolocator.getCurrentPosition();
          final cities = await _locationService.getCitiesAndClosest(position);

          final currentState = state;
          if (currentState is HomeLoaded) {
            _safeEmit(currentState.copyWith(
              currentCity: cities['closestCity'],
              cities: cities['allCities'],
              isLoadingCategories: true,
              isLoadingReels: true,
            ));
          } else {
            _safeEmit(HomeLoaded(
              currentCity: cities['closestCity'],
              cities: cities['allCities'],
              isLoadingCategories: true,
              isLoadingReels: true,
            ));
          }
          return;
        } catch (e) {
          developer.log('Failed to get current location: $e', name: 'HomeCubit');
          // Fall through to load cities without location
        }
      }

      // No permission or location failed - load cities with default location (Riyadh)
      await loadCitiesWithDefaultLocation();

    } catch (e) {
      developer.log('Failed to load cities: $e', name: 'HomeCubit');
      // Continue with empty cities list if everything fails
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(cities: []));
      } else {
        _safeEmit(const HomeLoaded(cities: []));
      }
    }
  }

  /// Load cities using default location (Riyadh coordinates)
  Future<void> loadCitiesWithDefaultLocation() async {
    try {
      // Use Riyadh coordinates as default
      const defaultLat = 24.7136;
      const defaultLng = 46.6753;

      final response = await _dioConsumer.post(
        '/api/general/cities',
        data: {'lat': defaultLat, 'lng': defaultLng},
      );

      if (response['data'] != null) {
        final data = response['data'];
        final List<dynamic> citiesData = data['cities'] as List;

        // Parse cities directly on main thread for better compatibility
        final citiesList = <City>[];
        try {
          for (final json in citiesData) {
            try {
              citiesList.add(City.fromJson(json));
            } catch (e) {
              developer.log('Error parsing city: $json, Error: $e', name: 'HomeCubit');
            }
          }
        } catch (e) {
          developer.log('Error parsing cities list: $e', name: 'HomeCubit');
        }

        // Select first city as default (usually closest to Riyadh)
        final defaultCity = citiesList.isNotEmpty ? citiesList.first : null;

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            currentCity: defaultCity,
            cities: citiesList,
            isLoadingCategories: true,
            isLoadingReels: true,
          ));
        } else {
          _safeEmit(HomeLoaded(
            currentCity: defaultCity,
            cities: citiesList,
            isLoadingCategories: true,
            isLoadingReels: true,
          ));
        }
      }
    } catch (e) {
      developer.log('Failed to load cities with default location: $e', name: 'HomeCubit');
      rethrow;
    }
  }

  /// Select a specific city and refetch data
  Future<void> selectCity(City city) async {
    developer.log('Selecting city: ${city.name} (ID: ${city.id})', name: 'HomeCubit');

    final currentState = state;
    if (currentState is HomeLoaded) {
      // Check if city is already selected to avoid unnecessary work
      if (currentState.currentCity?.id == city.id) {
        developer.log('City ${city.name} already selected, skipping refresh', name: 'HomeCubit');
        return;
      }

      // Update global city state
      try {
        final globalCityCubit = getIt<GlobalCityCubit>();
        await globalCityCubit.selectCity(city);
        developer.log('Updated global city state with: ${city.name} (ID: ${city.id})', name: 'HomeCubit');
      } catch (e) {
        developer.log('Error updating global city state: $e', name: 'HomeCubit');
      }

      // Update current city and set loading states
      _safeEmit(currentState.copyWith(
        currentCity: city,
        isLoadingCategories: true,
        isLoadingReels: true,
        categories: [], // Clear old data
        reels: [], // Clear old data
      ));

      developer.log('Starting to fetch data for city: ${city.id}', name: 'HomeCubit');

      // Refresh data with new city - sequential loading for better performance
      try {
        await fetchCategories(city.id, setLoading: false);
        await Future.delayed(const Duration(milliseconds: 100)); // Small delay
        await fetchReels(city.id, setLoading: false);
        developer.log('Successfully fetched data for city: ${city.id}', name: 'HomeCubit');
      } catch (e) {
        developer.log('Error fetching data for city ${city.id}: $e', name: 'HomeCubit');
      }
    }
  }

  /// Fetch categories with optional city filter
  Future<void> fetchCategories(int? cityId, {bool setLoading = true}) async {
    // Prevent duplicate API calls
    if (_isFetchingCategories) {
      developer.log('Categories fetch already in progress, skipping', name: 'HomeCubit');
      return;
    }

    _isFetchingCategories = true;

    final currentState = state;
    if (currentState is HomeLoaded && setLoading) {
      _safeEmit(currentState.copyWith(isLoadingCategories: true));
    }

    try {
      developer.log('Fetching categories with cityId: $cityId', name: 'HomeCubit');
      final queryParams = <String, dynamic>{
        'service_category_id': 1,
      };

      if (cityId != null) {
        queryParams['city_id'] = cityId;
      }

      final response = await _dioConsumer.get(
        '/api/service_categories/list',
        queryParameters: queryParams,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Categories API timeout');
        },
      );

      if (response['data'] != null) {
        final List data = response['data'];

        // Parse categories directly on main thread for better compatibility with older devices
        // The compute function can cause issues on some Android versions
        final categories = <ServiceCategory>[];
        try {
          for (final json in data) {
            try {
              categories.add(ServiceCategory.fromJson(json));
            } catch (e) {
              developer.log('Error parsing category: $json, Error: $e', name: 'HomeCubit');
              // Continue with other categories
            }
          }
        } catch (e) {
          developer.log('Error parsing categories list: $e', name: 'HomeCubit');
        }

        developer.log('Fetched ${categories.length} categories for cityId: $cityId', name: 'HomeCubit');

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            categories: categories,
            isLoadingCategories: false,
          ));
          developer.log('Updated state with ${categories.length} categories', name: 'HomeCubit');
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(isLoadingCategories: false));
      }
      // Don't emit error for individual operations, just log
      developer.log('Failed to load categories: $e', name: 'HomeCubit');
    } finally {
      _isFetchingCategories = false;
    }
  }

  /// Fetch reels with optional city filter and pagination
  Future<void> fetchReels(int? cityId, {bool setLoading = true, int page = 1, bool loadMore = false}) async {
    // Prevent duplicate API calls (but allow pagination)
    if (_isFetchingReels && !loadMore) {
      developer.log('Reels fetch already in progress, skipping', name: 'HomeCubit');
      return;
    }

    _isFetchingReels = true;

    final currentState = state;
    if (currentState is HomeLoaded && setLoading) {
      _safeEmit(currentState.copyWith(isLoadingReels: true));
    }

    try {
      developer.log('Fetching reels with cityId: $cityId, page: $page', name: 'HomeCubit');
      final queryParams = <String, dynamic>{
        'service_category_id': 2,
        'reels': 1,
        'page': page,
        'limit': 5, // Reduced from 10 to 5 for better performance on low-memory devices
      };

      if (cityId != null) {
        queryParams['city_id'] = cityId;
      }

      final response = await _dioConsumer.get(
        '/api/items/list',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];

        // Parse reels data directly on main thread for better compatibility
        final List<dynamic> itemsData = data['items'] ?? [];
        final List<Map<String, dynamic>> newReels = [];
        try {
          for (final item in itemsData) {
            if (item is Map<String, dynamic>) {
              newReels.add(item);
            }
          }
        } catch (e) {
          developer.log('Error parsing reels data: $e', name: 'HomeCubit');
        }
        final pagination = data['pagination'];

        developer.log('Fetched ${newReels.length} reels for cityId: $cityId, page: $page', name: 'HomeCubit');

        final currentState = state;
        if (currentState is HomeLoaded) {
          List<Map<String, dynamic>> updatedReels;
          if (loadMore && page > 1) {
            // Append new reels to existing ones
            updatedReels = [...currentState.reels, ...newReels];
          } else {
            // Replace reels (first page or refresh)
            updatedReels = newReels;
          }

          _safeEmit(currentState.copyWith(
            reels: updatedReels,
            isLoadingReels: false,
            reelsPagination: pagination,
          ));
          developer.log('Updated state with ${updatedReels.length} total reels', name: 'HomeCubit');
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(isLoadingReels: false));
      }
      // Don't emit error for individual operations, just log
      developer.log('Failed to load reels: $e', name: 'HomeCubit');
    } finally {
      _isFetchingReels = false;
    }
  }

  /// Load more reels for pagination
  Future<void> loadMoreReels() async {
    final currentState = state;
    if (currentState is HomeLoaded &&
        currentState.reelsPagination != null &&
        currentState.reelsPagination!['has_more'] == true &&
        !currentState.isLoadingReels) {

      final nextPage = (currentState.reelsPagination!['current_page'] as int) + 1;
      await fetchReels(
        currentState.currentCity?.id,
        setLoading: false,
        page: nextPage,
        loadMore: true
      );
    }
  }

  /// Perform search with optimized processing
  Future<void> performSearch(String query) async {
    if (query.trim().isEmpty) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(searchResults: []));
      }
      return;
    }

    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(isSearching: true));
    }

    try {
      final response = await _dioConsumer.get(
        '/api/items/search',
        queryParameters: {
          'keyword': query.trim(),
          'page': 1,
          'limit': 10, // Reduced from 20 to 10 for better performance
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<dynamic> itemsData = data['items'] ?? [];

        // Parse search results directly on main thread for better compatibility
        final List<Map<String, dynamic>> results = [];
        try {
          for (final item in itemsData) {
            if (item is Map<String, dynamic>) {
              results.add(item);
            }
          }
        } catch (e) {
          developer.log('Error parsing search results: $e', name: 'HomeCubit');
        }

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            searchResults: results,
            isSearching: false,
          ));
        }
      } else {
        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            searchResults: [],
            isSearching: false,
          ));
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(
          searchResults: [],
          isSearching: false,
        ));
      }
    }
  }

  /// Clear search results
  void clearSearch() {
    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(searchResults: []));
    }
  }

  /// Update localization and refresh data
  void updateLocalization() {
    _dioConsumer.updateLocalization();
    
    final currentState = state;
    if (currentState is HomeLoaded) {
      // Refresh data with current city
      Future.wait([
        fetchCategories(currentState.currentCity?.id, setLoading: true),
        fetchReels(currentState.currentCity?.id, setLoading: true),
      ]);
    }
  }
}
