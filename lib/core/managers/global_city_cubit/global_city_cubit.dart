import 'dart:developer' as developer;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:hive/hive.dart';

part 'global_city_state.dart';

class GlobalCityCubit extends Cubit<GlobalCityState> {
  static const String _selectedCityBoxName = 'selected_city_box';
  static const String _selectedCityKey = 'selected_city_key';
  
  GlobalCityCubit() : super(GlobalCityInitial()) {
    _loadSavedCity();
  }

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(GlobalCityState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Load the saved city from local storage
  Future<void> _loadSavedCity() async {
    try {
      final box = await Hive.openBox<Map>(_selectedCityBoxName);
      final savedCityData = box.get(_selectedCityKey);
      
      if (savedCityData != null) {
        final city = City.fromJson(Map<String, dynamic>.from(savedCityData));
        developer.log('GlobalCityCubit: Loaded saved city: ${city.name} (ID: ${city.id})', name: 'GlobalCityCubit');
        _safeEmit(GlobalCitySelected(city));
      } else {
        developer.log('GlobalCityCubit: No saved city found', name: 'GlobalCityCubit');
        _safeEmit(GlobalCityInitial());
      }
    } catch (e) {
      developer.log('GlobalCityCubit: Error loading saved city: $e', name: 'GlobalCityCubit');
      _safeEmit(GlobalCityInitial());
    }
  }

  /// Select a city and save it globally
  Future<void> selectCity(City city) async {
    try {
      developer.log('GlobalCityCubit: Selecting city: ${city.name} (ID: ${city.id})', name: 'GlobalCityCubit');
      
      // Save to local storage
      final box = await Hive.openBox<Map>(_selectedCityBoxName);
      await box.put(_selectedCityKey, city.toJson());
      
      // Emit new state
      _safeEmit(GlobalCitySelected(city));
      
      developer.log('GlobalCityCubit: City selected and saved successfully', name: 'GlobalCityCubit');
    } catch (e) {
      developer.log('GlobalCityCubit: Error selecting city: $e', name: 'GlobalCityCubit');
    }
  }

  /// Clear the selected city
  Future<void> clearSelectedCity() async {
    try {
      final box = await Hive.openBox<Map>(_selectedCityBoxName);
      await box.delete(_selectedCityKey);
      
      _safeEmit(GlobalCityInitial());
      developer.log('GlobalCityCubit: Selected city cleared', name: 'GlobalCityCubit');
    } catch (e) {
      developer.log('GlobalCityCubit: Error clearing selected city: $e', name: 'GlobalCityCubit');
    }
  }

  /// Get the currently selected city (null if none selected)
  City? get selectedCity {
    final currentState = state;
    if (currentState is GlobalCitySelected) {
      return currentState.city;
    }
    return null;
  }

  @override
  Future<void> close() async {
    try {
      final box = Hive.box<Map>(_selectedCityBoxName);
      await box.close();
    } catch (e) {
      developer.log('GlobalCityCubit: Error closing box: $e', name: 'GlobalCityCubit');
    }
    return super.close();
  }
}
