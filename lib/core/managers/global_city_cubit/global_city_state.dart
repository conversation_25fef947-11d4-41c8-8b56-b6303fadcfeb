part of 'global_city_cubit.dart';

abstract class GlobalCityState {
  const GlobalCityState();
}

class GlobalCityInitial extends GlobalCityState {
  const GlobalCityInitial();
}

class GlobalCitySelected extends GlobalCityState {
  final City city;

  const GlobalCitySelected(this.city);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GlobalCitySelected && other.city.id == city.id;
  }

  @override
  int get hashCode => city.id.hashCode;
}
